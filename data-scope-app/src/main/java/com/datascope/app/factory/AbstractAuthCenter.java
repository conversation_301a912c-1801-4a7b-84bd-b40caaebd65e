package com.datascope.app.factory;

import cn.hutool.core.lang.Pair;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.model.ResourceBO;
import com.datascope.app.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractAuthCenter {

    @Autowired
    public AuthService authService;

    public void process(AuthDTO authDTO) {
        Pair<Boolean, Boolean> handle = handle(authDTO);
        compareAuthCenter(handle, authDTO);
    }

    /**
     * 执行逻辑
     *
     * @param authDTO AuthDTO
     * @return Pair
     */
    public abstract Pair<Boolean, Boolean> handle(AuthDTO authDTO);

    /**
     * 授权类型
     *
     * @return String
     */
    public abstract String getAuthType();

    /**
     * 添加资源
     *
     * @param authDTO 入参
     * @return ResourceBO
     */
    public abstract ResourceBO addAuthResource(AuthDTO authDTO);

    /**
     * 删除资源
     *
     * @param authDTO 入参
     * @return ResourceBO
     */
    public abstract ResourceBO removeAuthResource(AuthDTO authDTO);


    public void compareAuthCenter(Pair<Boolean, Boolean> pair, AuthDTO authDTO) {
        // compare auth center
        if (Objects.isNull(pair.getValue())) {
            return;
        }
        Boolean key = pair.getKey();
        boolean value = pair.getValue();
        if (!key && value) {
            // add resource
            ResourceBO resourceBO = addAuthResource(authDTO);
            commonResource(resourceBO);
            authService.callAddOrDelResource(resourceBO, Constant.UrlPath.ADD_RESOURCE);
        } else if (key && !value) {
            // del resource
//            ResourceBO resourceBO = removeAuthResource(authDTO);
//            commonResource(resourceBO);
//            authService.callAddOrDelResource(resourceBO, Constant.UrlPath.DEL_RESOURCE);
        }
    }

    private void commonResource(ResourceBO resourceBO) {
        resourceBO.setResourceStatus("enabled");
        resourceBO.setSystemCode("data-scope");
        resourceBO.setMajorCode("dataBaseGroup");
        resourceBO.setCategoryCode("tableType");
    }
}
