package com.datascope.app.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.datascope.app.dto.metadata.*;
import com.datascope.app.entity.*;
import com.datascope.app.factory.AbstractAuthCenter;
import com.datascope.app.factory.AuthFactory;
import com.datascope.app.common.BusinessException;
import com.datascope.app.mapper.*;
import com.datascope.app.service.MetadataService;
import com.datascope.app.service.TableDataQueryService;
import com.datascope.app.util.AuthUtils;
import com.datascope.app.util.JdbcUrlBuilder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 元数据服务实现类
 *
 * 新增功能：
 * - 支持MySQL连接下加载所有数据库的元数据
 * - 通过SyncMetadataRequest.filters.loadAllDatabases=true启用
 * - 自动跳过MySQL系统数据库（information_schema, performance_schema, mysql, sys）
 * - 支持数据库级别的包含/排除过滤
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MetadataServiceImpl implements MetadataService {

    private final MetadataSyncMapper metadataSyncMapper;
    private final SchemaMapper schemaMapper;
    private final TableMapper tableMapper;
    private final ColumnMapper columnMapper;
    private final DatasourceMapper datasourceMapper;
    private final TableDataQueryService tableDataQueryService;
    private final ObjectMapper objectMapper;
    private final AuthFactory authFactory;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SyncMetadataResponse syncMetadata(String dataSourceId, SyncMetadataRequest request) {
        log.info("开始同步数据源[{}]的元数据", dataSourceId);

        // 创建同步记录
        String syncId = UUID.randomUUID().toString().replace("-", "");
        Date startTime = new Date();

        MetadataSync metadataSync = MetadataSync.builder()
                .id(syncId)
                .datasourceId(dataSourceId)
                .startTime(startTime)
                .status("running")
            .createdBy(AuthUtils.getUsername())
                .createdAt(startTime)
                .build();

        metadataSyncMapper.insert(metadataSync);

        try {
            // 实现实际的同步逻辑
            // 获取数据源信息
            Datasource datasource = getDatasource(dataSourceId);
            if (datasource == null) {
                throw new RuntimeException("数据源不存在：" + dataSourceId);
            }

            log.info("开始从数据源[{}]提取元数据", datasource.getName());

            // 构建数据库连接
            String jdbcUrl = JdbcUrlBuilder.buildJdbcUrl(datasource);
            Properties connectionProps = new Properties();
            connectionProps.put("user", datasource.getUsername());
            connectionProps.put("password", datasource.getPassword());

            // 添加过滤器配置
            SyncMetadataRequest.FiltersConfig filters = request.getFilters();
            List<String> includeSchemas = filters != null ? filters.getIncludeSchemas() : Lists.newLinkedList();
            List<String> excludeSchemas = filters != null ? filters.getExcludeSchemas() : null;
            List<String> includeTables = filters != null ? filters.getIncludeTables() : null;
            List<String> excludeTables = filters != null ? filters.getExcludeTables() : null;

            // 如果指定了 Schema 则过滤
            if (StringUtils.isNotBlank(datasource.getSchema())) {
                includeSchemas.add(datasource.getSchema());
            }

            try (Connection connection = DriverManager.getConnection(jdbcUrl, connectionProps)) {
                DatabaseMetaData dbMetaData = connection.getMetaData();

                // 获取数据库产品信息
                String databaseProductName = dbMetaData.getDatabaseProductName();
                String databaseVersion = dbMetaData.getDatabaseProductVersion();

                log.info("连接到数据库：{} {}", databaseProductName, databaseVersion);

                // 获取模式信息
                List<Schema> schemas = new ArrayList<>();
                AtomicInteger tablesTotal = new AtomicInteger(0);
                AtomicInteger viewsTotal = new AtomicInteger(0);

                // 准备差量更新所需的数据结构
                List<Schema> newSchemas = new ArrayList<>();
                Map<String, List<Table>> newTables = new HashMap<>();
                Map<String, List<Column>> newColumns = new HashMap<>();

                // 提取schema
                log.info("开始提取数据库[{}]的schema信息", datasource.getDatabaseName());

                // 对于MySQL，需要特殊处理，因为MySQL中的schema等同于database
                if ("mysql".equalsIgnoreCase(datasource.getType())) {
                    log.info("开始处理MySQL数据库[{}]的元数据", datasource.getDatabaseName());

                    // 检查是否需要加载所有数据库
//                    boolean loadAllDatabases = request.getFilters() != null &&
//                                             Boolean.TRUE.equals(request.getFilters().getLoadAllDatabases());
                    boolean loadAllDatabases = true;

                    if (loadAllDatabases) {
                        log.info("启用加载MySQL连接下的所有数据库");
                        // 获取MySQL连接下的所有数据库
                        try (ResultSet catalogRs = dbMetaData.getCatalogs()) {
                            while (catalogRs.next()) {
                                String catalogName = catalogRs.getString("TABLE_CAT");

                                // 跳过系统数据库
                                if (shouldSkipSystemDatabase(catalogName)) {
                                    log.debug("跳过系统数据库: {}", catalogName);
                                    continue;
                                }

                                // 应用数据库过滤条件
                                if (shouldSkipSchema(catalogName, includeSchemas, excludeSchemas)) {
                                    log.debug("跳过数据库（根据过滤条件）: {}", catalogName);
                                    continue;
                                }

                                log.info("处理MySQL数据库: {}", catalogName);

                                // 为每个数据库创建Schema记录
                                String schemaId = UUID.randomUUID().toString().replace("-", "");
                                Schema schema = Schema.builder()
                                        .id(schemaId)
                                        .datasourceId(dataSourceId)
                                        .name(catalogName)
                                        .tablesCount(0)
                                        .createdAt(LocalDateTime.now())
                                        .updatedAt(LocalDateTime.now())
                                        .build();
                                schemas.add(schema);
                                newSchemas.add(schema);

                                // 获取该数据库的表信息
                                log.info("开始提取数据库[{}]的表信息", catalogName);
                                extractMySQLTablesWithCatalog(dbMetaData, schema, catalogName, includeTables, excludeTables, tablesTotal, viewsTotal, newTables, newColumns);

                                // 如果没有提取到表，尝试第二种方式（切换到该数据库再查询）
                                if (schema.getTablesCount() == 0) {
                                    log.info("尝试切换到数据库[{}]后再次获取表信息", catalogName);
                                    try (Statement useDbStmt = connection.createStatement()) {
                                        useDbStmt.execute("USE " + catalogName);
                                        extractMySQLTablesWithoutCatalog(dbMetaData, schema, includeTables, excludeTables, tablesTotal, viewsTotal, newTables, newColumns);
                                    } catch (SQLException e) {
                                        log.warn("切换到数据库[{}]失败: {}", catalogName, e.getMessage());
                                    }
                                }

                                log.info("数据库[{}]共提取到{}个表", catalogName, schema.getTablesCount());
                            }
                        }
                    } else {
                        // 原有逻辑：只处理配置的单个数据库
                        log.info("处理配置的单个数据库: {}", datasource.getDatabaseName());

                        // 1. 创建Schema记录表示当前数据库
                        String schemaId = UUID.randomUUID().toString().replace("-", "");
                        Schema schema = Schema.builder()
                                .id(schemaId)
                                .datasourceId(dataSourceId)
                                .name(datasource.getDatabaseName())
                                .tablesCount(0)
                                .createdAt(LocalDateTime.now())
                                .updatedAt(LocalDateTime.now())
                                .build();
                        schemas.add(schema);

                        // 添加到新schema列表
                        log.info("添加MySQL数据库schema到差量更新列表: {}", schema);
                        newSchemas.add(schema);

                        // 2. 直接获取当前数据库的表
                        // MySQL中可以用两种方式，第一种：getTables(catalog, schema, tableName, types)
                        // 第二种：getTables(null, null, tableName, types)
                        log.info("尝试第一种方式：通过catalog获取表");
                        extractMySQLTablesWithCatalog(dbMetaData, schema, datasource.getDatabaseName(), includeTables, excludeTables, tablesTotal, viewsTotal, newTables, newColumns);

                        // 如果第一种方式没有提取到表，则尝试第二种方式
                        if (schema.getTablesCount() == 0) {
                            log.info("尝试第二种方式：不指定catalog获取表");
                            extractMySQLTablesWithoutCatalog(dbMetaData, schema, includeTables, excludeTables, tablesTotal, viewsTotal, newTables, newColumns);
                        }
                    }
                } else {
                    // 其他数据库按照标准方式处理
                    try (ResultSet schemaRs = dbMetaData.getSchemas()) {
                        while (schemaRs.next()) {
                            String schemaName = schemaRs.getString("TABLE_SCHEM");

                            // 应用模式过滤条件
                            if (shouldSkipSchema(schemaName, includeSchemas, excludeSchemas)) {
                                continue;
                            }

                            log.info("处理模式：{}", schemaName);

                            // 创建Schema记录
                            String schemaId = UUID.randomUUID().toString().replace("-", "");
                            Schema schema = Schema.builder()
                                    .id(schemaId)
                                    .datasourceId(dataSourceId)
                                    .name(schemaName)
                                    .tablesCount(0)
                                    .createdAt(LocalDateTime.now())
                                    .updatedAt(LocalDateTime.now())
                                    .build();
                            schemas.add(schema);

                            // 添加到新schema列表
                            log.info("添加schema到差量更新列表: {}", schema);
                            newSchemas.add(schema);

                            // 获取表信息
                            extractTablesForSchema(dbMetaData, schema, schemaName, includeTables, excludeTables, tablesTotal, viewsTotal, newTables, newColumns);
                        }
                    }
                }

                // 执行差量更新
                log.info("开始执行差量更新，共有{}个schema, {}个table, {}个列", newSchemas.size(),
                    newTables.values().stream().mapToInt(List::size).sum(),
                    newColumns.values().stream().mapToInt(List::size).sum());
                incrementalUpdateMetadata(datasource, newSchemas, newTables, newColumns);

                // 更新同步记录
                Date endTime = new Date();
                int syncDuration = (int) ((endTime.getTime() - startTime.getTime()/1000));

                metadataSync.setEndTime(endTime);
                metadataSync.setStatus("completed");
                metadataSync.setTablesCount(tablesTotal.get());
                metadataSync.setViewsCount(viewsTotal.get());
                metadataSync.setSyncDuration(syncDuration * 1000);
                metadataSync.setMessage("同步成功，发现 " + schemas.size() + " 个模式, "
                        + tablesTotal.get() + " 个表, " + viewsTotal.get() + " 个视图");

                metadataSyncMapper.updateById(metadataSync);

                // 更新数据源的同步状态
                updateDatasourceLastSyncTime(dataSourceId, endTime);

                // 返回结果
                return SyncMetadataResponse.builder()
                        .success(true)
                        .syncId(syncId)
                        .dataSourceId(dataSourceId)
                        .startTime(startTime)
                        .endTime(endTime)
                        .tablesCount(tablesTotal.get())
                        .viewsCount(viewsTotal.get())
                        .syncDuration(syncDuration * 1000)
                        .status(metadataSync.getStatus())
                        .message(metadataSync.getMessage())
                        .errors(new ArrayList<>())
                        .build();
            }

        } catch (Exception e) {
            log.error("同步数据源[{}]元数据失败", dataSourceId, e);

            // 更新同步记录为失败状态
            Date endTime = new Date();
            int syncDuration = (int) ((endTime.getTime() - startTime.getTime()/1000));

            metadataSync.setEndTime(endTime);
            metadataSync.setStatus("failed");
            metadataSync.setSyncDuration(syncDuration);
            metadataSync.setMessage("同步失败：" + e.getMessage());

            metadataSyncMapper.updateById(metadataSync);

            List<String> errors = new ArrayList<>();
            errors.add(e.getMessage());

            // 返回失败结果
            return SyncMetadataResponse.builder()
                    .success(false)
                    .syncId(syncId)
                    .dataSourceId(dataSourceId)
                    .startTime(startTime)
                    .endTime(endTime)
                    .syncDuration(syncDuration)
                    .status(metadataSync.getStatus())
                    .message(metadataSync.getMessage())
                    .errors(errors)
                    .build();
        }
    }

    @Override
    public List<SchemaDTO> getSchemas(String dataSourceId) {
        LambdaQueryWrapper<Schema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Schema::getDatasourceId, dataSourceId);

        List<Schema> schemas = schemaMapper.selectList(queryWrapper);

        return schemas.stream()
                .map(schema -> {
                    SchemaDTO dto = new SchemaDTO();
                    BeanUtils.copyProperties(schema, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<TableDTO> getTables(String schemaId) {
        LambdaQueryWrapper<Table> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Table::getSchemaId, schemaId);

        List<Table> tables = tableMapper.selectList(queryWrapper);

        return tables.stream()
                .map(table -> {
                    TableDTO dto = new TableDTO();
                    BeanUtils.copyProperties(table, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ColumnDTO> getColumns(String tableId) {
        log.info("查询表[{}]的列信息", tableId);
        if (tableId == null || tableId.isEmpty()) {
            log.warn("表ID为空，无法查询列信息");
            return Lists.newArrayList();
        }

        // 查询表的所有列信息
        LambdaQueryWrapper<Column> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Column::getTableId, tableId);
        queryWrapper.orderByAsc(Column::getPosition);
        List<Column> columns = columnMapper.selectList(queryWrapper);

        // 转换为DTO对象
        return columns.stream()
                .map(column ->
                {
                    ColumnDTO build = ColumnDTO.builder()
                        .id(column.getId())
                        .name(column.getName())
                        .dataType(column.getDataType())
                        .columnType(column.getColumnType())
                        .position(column.getPosition())
                        .isNullable(column.getIsNullable())
                        .isPrimaryKey(column.getIsPrimaryKey())
                        .isUnique(column.getIsUnique())
                        .isIndexed(column.getIsIndexed())
                        .isEncrypted(column.getIsEncrypted())
                        .isAuthRequired(column.getIsAuthRequired())
                        .defaultValue(column.getDefaultValue())
                        .characterLength(column.getCharacterLength())
                        .numericPrecision(column.getNumericPrecision())
                        .numericScale(column.getNumericScale())
                        .description(column.getDescription())
                        .build();
                    if (StrUtil.isNotEmpty(column.getEncryConfig())) {
                        try {
                            build.setEntryConfig(objectMapper.readValue(column.getEncryConfig(), Object.class));
                        } catch (Exception e) {
                            log.error("entryConfig parse error: {}", column.getId(), e);
                        }
                    }
                    return build;
                }).collect(Collectors.toList());
    }

    @Override
    public TableDataResponse getTableData(String tableId, TableDataQueryRequest request) {
        log.debug("开始查询表[{}]数据，参数：{}", tableId, request);
        return tableDataQueryService.queryTableData(tableId, request);
    }

    /**
     * 获取数据源信息
     *
     * @param dataSourceId 数据源ID
     * @return 数据源实体
     */
    private Datasource getDatasource(String dataSourceId) {
        return datasourceMapper.selectById(dataSourceId);
    }

    /**
     * 判断是否应该跳过指定的Schema
     */
    private boolean shouldSkipSchema(String schemaName, List<String> includeSchemas, List<String> excludeSchemas) {
        // 如果指定了包含列表，只处理包含列表中的schema
        if (includeSchemas != null && !includeSchemas.isEmpty()) {
            return !includeSchemas.contains(schemaName);
        }

        // 如果指定了排除列表，跳过排除列表中的schema
        if (excludeSchemas != null && !excludeSchemas.isEmpty()) {
            return excludeSchemas.contains(schemaName);
        }

        // 默认跳过常见的系统模式
        Set<String> systemSchemas = new HashSet<>();
        systemSchemas.add("information_schema");
        systemSchemas.add("performance_schema");
        systemSchemas.add("mysql");
        systemSchemas.add("sys");
        systemSchemas.add("pg_catalog");
        systemSchemas.add("pg_information_schema");
        systemSchemas.add("master");
        systemSchemas.add("model");
        systemSchemas.add("msdb");
        systemSchemas.add("tempdb");

        return systemSchemas.contains(schemaName.toLowerCase());
    }

    /**
     * 判断是否应该跳过指定的MySQL系统数据库
     */
    private boolean shouldSkipSystemDatabase(String databaseName) {
        if (databaseName == null || databaseName.trim().isEmpty()) {
            return true;
        }

        // MySQL系统数据库列表
        Set<String> systemDatabases = new HashSet<>();
        systemDatabases.add("information_schema");
        systemDatabases.add("performance_schema");
        systemDatabases.add("mysql");
        systemDatabases.add("sys");

        return systemDatabases.contains(databaseName.toLowerCase());
    }

    /**
     * 判断是否应该跳过该表
     *
     * @param tableName 表名称
     * @param includeTables 包含的表列表
     * @param excludeTables 排除的表列表
     * @return 是否应该跳过
     */
    private boolean shouldSkipTable(String tableName, List<String> includeTables, List<String> excludeTables) {
        // 如果存在包含列表且不为空，则必须在包含列表中
        if (!CollectionUtils.isEmpty(includeTables) && !includeTables.contains(tableName)) {
            return true;
        }

        // 如果存在排除列表且不为空，则不能在排除列表中
        return !CollectionUtils.isEmpty(excludeTables) && excludeTables.contains(tableName);
    }

    /**
     * 更新数据源的最后同步时间
     *
     * @param dataSourceId 数据源ID
     * @param syncTime 同步时间
     */
    private void updateDatasourceLastSyncTime(String dataSourceId, Date syncTime) {
        Datasource datasource = datasourceMapper.selectById(dataSourceId);
        if (datasource != null) {
            datasource.setLastSyncTime(syncTime);
            datasource.setStatus("active");
            datasourceMapper.updateById(datasource);
        }
    }

    /**
     * 差量更新元数据
     *
     * @param datasource 数据源
     * @param newSchemas 新的schema列表
     * @param newTables  新的表列表（按schema分组）
     * @param newColumns 新的列列表（按表分组）
     */
    private void incrementalUpdateMetadata(Datasource datasource, List<Schema> newSchemas,
                                           Map<String, List<Table>> newTables,
                                           Map<String, List<Column>> newColumns) {
        log.info("开始差量更新数据源[{}]的元数据", datasource);

        // 1. 处理Schema层级的差量更新
        updateSchemas(datasource, newSchemas);

        // 2. 处理Table层级的差量更新
        updateTables(newSchemas, newTables, newColumns);

        // 3. 处理Column层级的差量更新
        updateColumns(newTables, newColumns);

        log.info("数据源[{}]的元数据差量更新完成", datasource);
    }

    /**
     * 更新Schema
     *
     * @param datasource 数据源
     * @param newSchemas 新的schema列表
     */
    private void updateSchemas(Datasource datasource, List<Schema> newSchemas) {
        // 获取数据库中已存在的schema
        LambdaQueryWrapper<Schema> schemaWrapper = new LambdaQueryWrapper<>();
        schemaWrapper.eq(Schema::getDatasourceId, datasource.getId());
        List<Schema> existingSchemas = schemaMapper.selectList(schemaWrapper);

        // 如果数据源指定了schema，则只更新指定的schema
        String specifiedSchema = datasource.getSchema();
        if (StringUtils.isNotBlank(specifiedSchema)) {
            log.info("数据源[{}]指定了schema: {}，只更新该schema", datasource.getId(), specifiedSchema);

            // 过滤新的schema列表，只保留指定的schema
            List<Schema> filteredNewSchemas = newSchemas.stream()
                .filter(schema -> specifiedSchema.equals(schema.getName()))
                .collect(Collectors.toList());

            // 过滤已存在的schema列表，只保留指定的schema
            List<Schema> filteredExistingSchemas = existingSchemas.stream()
                .filter(schema -> specifiedSchema.equals(schema.getName()))
                .collect(Collectors.toList());

            // 使用过滤后的列表进行更新
            updateSchemasList(filteredNewSchemas, filteredExistingSchemas);
        } else {
            // 如果数据源没有指定schema，则更新所有schema
            log.info("数据源[{}]没有指定schema，更新所有schema", datasource.getId());
            updateSchemasList(newSchemas, existingSchemas);
        }
    }

    /**
     * 更新Schema列表
     *
     * @param newSchemas      新的schema列表
     * @param existingSchemas 已存在的schema列表
     */
    private void updateSchemasList(List<Schema> newSchemas, List<Schema> existingSchemas) {
        // 将已存在的schema转换为Map，方便查找
        Map<String, Schema> existingSchemaMap = existingSchemas.stream()
            .collect(Collectors.toMap(Schema::getName, schema -> schema));

        // 将新的schema转换为Map，方便查找
        Map<String, Schema> newSchemaMap = newSchemas.stream()
            .collect(Collectors.toMap(Schema::getName, schema -> schema));

        // 找出需要新增的schema
        List<Schema> schemasToAdd = newSchemas.stream()
            .filter(schema -> !existingSchemaMap.containsKey(schema.getName()))
            .collect(Collectors.toList());

        // 找出需要删除的schema
        List<Schema> schemasToDelete = existingSchemas.stream()
            .filter(schema -> !newSchemaMap.containsKey(schema.getName()))
            .collect(Collectors.toList());

        // 找出需要更新的schema
        List<Schema> schemasToUpdate = newSchemas.stream()
            .filter(schema -> existingSchemaMap.containsKey(schema.getName()))
            .map(schema -> {
                Schema existingSchema = existingSchemaMap.get(schema.getName());
                schema.setId(existingSchema.getId()); // 保留原ID
                return schema;
            })
            .collect(Collectors.toList());

        // 执行新增操作
        if (!schemasToAdd.isEmpty()) {
            log.info("新增{}个schema", schemasToAdd.size());
            for (Schema schema : schemasToAdd) {
                log.info("新增schema: {}", schema.getName());
                // 保存schema并获取数据库生成的ID
                schemaMapper.insert(schema);
                log.info("新增schema后的ID: {}", schema.getId());
            }
        }

        // 执行更新操作
        if (!schemasToUpdate.isEmpty()) {
            log.info("更新{}个schema", schemasToUpdate.size());
            for (Schema schema : schemasToUpdate) {
                log.info("更新schema: {}", schema.getName());
                schemaMapper.updateById(schema);
            }
        }

        // 执行删除操作
        if (!schemasToDelete.isEmpty()) {
            log.info("删除{}个schema", schemasToDelete.size());
            for (Schema schema : schemasToDelete) {
                log.info("删除schema: {}", schema.getName());
                // 删除schema下的所有表和列
                deleteSchemaWithTablesAndColumns(schema);
            }
        }
    }

    /**
     * 删除schema及其下的所有表和列
     *
     * @param schema 要删除的schema
     */
    private void deleteSchemaWithTablesAndColumns(Schema schema) {
        // 查询该schema下的所有表
        LambdaQueryWrapper<Table> tableWrapper = new LambdaQueryWrapper<>();
        tableWrapper.eq(Table::getSchemaId, schema.getId());
        List<Table> tables = tableMapper.selectList(tableWrapper);

        // 删除每个表下的列
        for (Table table : tables) {
            LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
            columnWrapper.eq(Column::getTableId, table.getId());
            columnMapper.delete(columnWrapper);
        }

        // 删除所有表
        tableMapper.delete(tableWrapper);

        // 删除schema
        schemaMapper.deleteById(schema.getId());
    }

    /**
     * 更新表
     *
     * @param newSchemas 新的schema列表
     * @param newTables  新的表列表（按schema分组）
     * @param newColumns 新的列列表（按表分组）
     */
    private void updateTables(List<Schema> newSchemas, Map<String, List<Table>> newTables, Map<String, List<Column>> newColumns) {
        for (Schema schema : newSchemas) {
            // 获取该schema下的新表列表
            // 使用schema的名称作为key，与添加表时保持一致
            String schemaKey = schema.getName();
            List<Table> schemaTables = newTables.getOrDefault(schemaKey, Collections.emptyList());

            // 获取数据库中该schema下已存在的表
            LambdaQueryWrapper<Table> tableWrapper = new LambdaQueryWrapper<>();
            tableWrapper.eq(Table::getSchemaId, schema.getId());
            List<Table> existingTables = tableMapper.selectList(tableWrapper);

            // 将已存在的表转换为Map，方便查找
            Map<String, Table> existingTableMap = existingTables.stream()
                .collect(Collectors.toMap(Table::getName, table -> table));

            // 将新的表转换为Map，方便查找
            Map<String, Table> newTableMap = schemaTables.stream()
                .collect(Collectors.toMap(Table::getName, table -> table));

            // 找出需要新增的表
            List<Table> tablesToAdd = schemaTables.stream()
                .filter(table -> !existingTableMap.containsKey(table.getName()))
                .map(table -> {
                    // 更新表的schemaId为数据库中的schema ID
                    table.setSchemaId(schema.getId());
                    return table;
                })
                .collect(Collectors.toList());

            // 找出需要删除的表
            List<Table> tablesToDelete = existingTables.stream()
                .filter(table -> !newTableMap.containsKey(table.getName()))
                .collect(Collectors.toList());

            // 找出需要更新的表
            List<Table> tablesToUpdate = schemaTables.stream()
                .filter(table -> existingTableMap.containsKey(table.getName()))
                .map(table -> {
                    Table existingTable = existingTableMap.get(table.getName());
                    String oldId = table.getId(); // 保存旧ID
                    table.setId(existingTable.getId()); // 保留原ID
                    table.setSchemaId(schema.getId()); // 更新表的schemaId为数据库中的schema ID

                    // 更新newColumns中的映射关系
                    List<Column> columns = newColumns.remove(oldId);
                    if (columns != null && !columns.isEmpty()) {
                        log.info("更新表[{}]的列映射关系，旧ID: {}, 新ID: {}, 列数: {}",
                            table.getName(), oldId, table.getId(), columns.size());
                        newColumns.put(table.getId(), columns);

                        // 更新列的tableId
                        for (Column column : columns) {
                            column.setTableId(table.getId());
                        }
                    }

                    return table;
                })
                .collect(Collectors.toList());

            // 执行新增操作
            if (!tablesToAdd.isEmpty()) {
                log.info("在schema[{}]中新增{}个表", schema.getName(), tablesToAdd.size());
                for (Table table : tablesToAdd) {
                    String oldId = table.getId(); // 保存旧ID
                    log.info("新增表: {}", table.getName());
                    tableMapper.insert(table);

                    // 更新newColumns中的映射关系
                    List<Column> columns = newColumns.remove(oldId);
                    if (columns != null && !columns.isEmpty()) {
                        log.info("更新表[{}]的列映射关系，旧ID: {}, 新ID: {}, 列数: {}",
                            table.getName(), oldId, table.getId(), columns.size());
                        newColumns.put(table.getId(), columns);

                        // 更新列的tableId
                        for (Column column : columns) {
                            column.setTableId(table.getId());
                        }
                    }
                }
            }

            // 执行更新操作
            if (!tablesToUpdate.isEmpty()) {
                log.info("在schema[{}]中更新{}个表", schema.getName(), tablesToUpdate.size());
                for (Table table : tablesToUpdate) {
                    log.info("更新表: {}", table.getName());
                    tableMapper.updateById(table);
                }
            }

            // 执行删除操作
            if (!tablesToDelete.isEmpty()) {
                log.info("在schema[{}]中删除{}个表", schema.getName(), tablesToDelete.size());
                for (Table table : tablesToDelete) {
                    log.info("删除表: {}", table.getName());
                    // 删除表下的所有列
                    LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
                    columnWrapper.eq(Column::getTableId, table.getId());
                    columnMapper.delete(columnWrapper);

                    // 删除表
                    tableMapper.deleteById(table.getId());
                }
            }
        }
    }

    /**
     * 更新列
     *
     * @param newTables  新的表列表（按schema分组）
     * @param newColumns 新的列列表（按表分组）
     */
    private void updateColumns(Map<String, List<Table>> newTables, Map<String, List<Column>> newColumns) {
        // 遍历所有表
        for (List<Table> tables : newTables.values()) {
            for (Table table : tables) {
                // 获取该表下的新列列表
                List<Column> tableColumns = newColumns.getOrDefault(table.getId(), Collections.emptyList());

                // 获取数据库中该表下已存在的列
                LambdaQueryWrapper<Column> columnWrapper = new LambdaQueryWrapper<>();
                columnWrapper.eq(Column::getTableId, table.getId());
                List<Column> existingColumns = columnMapper.selectList(columnWrapper);

                // 将已存在的列转换为Map，方便查找
                Map<String, Column> existingColumnMap = existingColumns.stream()
                    .collect(Collectors.toMap(Column::getName, column -> column));

                // 将新的列转换为Map，方便查找
                Map<String, Column> newColumnMap = tableColumns.stream()
                    .collect(Collectors.toMap(Column::getName, column -> column));

                // 找出需要新增的列
                List<Column> columnsToAdd = tableColumns.stream()
                    .filter(column -> !existingColumnMap.containsKey(column.getName()))
                    .collect(Collectors.toList());

                // 找出需要删除的列
                List<Column> columnsToDelete = existingColumns.stream()
                    .filter(column -> !newColumnMap.containsKey(column.getName()))
                    .collect(Collectors.toList());

                // 找出需要更新的列
                List<Column> columnsToUpdate = tableColumns.stream()
                    .filter(column -> existingColumnMap.containsKey(column.getName()))
                    .map(column -> {
                        Column existingColumn = existingColumnMap.get(column.getName());
                        // 保留原ID
                        column.setId(existingColumn.getId());
                        // 保留用户配置的加密设置
                        if (existingColumn.getIsEncrypted() != null) {
                            column.setIsEncrypted(existingColumn.getIsEncrypted());
                        }
                        return column;
                    })
                    .collect(Collectors.toList());

                // 执行新增操作
                if (!columnsToAdd.isEmpty()) {
                    log.info("在表[{}]中新增{}个列", table.getName(), columnsToAdd.size());
                    for (Column column : columnsToAdd) {
                        log.debug("新增列: {}", column.getName());
                        columnMapper.insert(column);
                    }
                }

                // 执行更新操作
                if (!columnsToUpdate.isEmpty()) {
                    log.info("在表[{}]中更新{}个列", table.getName(), columnsToUpdate.size());
                    for (Column column : columnsToUpdate) {
                        log.debug("更新列: {}", column.getName());
                        columnMapper.updateById(column);
                    }
                }

                // 执行删除操作
                if (!columnsToDelete.isEmpty()) {
                    log.info("在表[{}]中删除{}个列", table.getName(), columnsToDelete.size());
                    for (Column column : columnsToDelete) {
                        log.debug("删除列: {}", column.getName());
                        columnMapper.deleteById(column.getId());
                    }
                }
            }
        }
    }

    private void extractTablesForSchema(DatabaseMetaData dbMetaData, Schema schema, String schemaName,
                                      List<String> includeTables, List<String> excludeTables,
                                        AtomicInteger tablesTotal, AtomicInteger viewsTotal,
                                        Map<String, List<Table>> newTables, Map<String, List<Column>> newColumns) throws SQLException {
        log.info("开始提取schema[{}]的表信息", schemaName);

        // 获取表信息
        List<Table> tables = new ArrayList<>();
        try (ResultSet tableRs = dbMetaData.getTables(
                null, schemaName, "%", new String[]{"TABLE", "VIEW"})) {

            while (tableRs.next()) {
                String tableName = tableRs.getString("TABLE_NAME");
                String tableType = tableRs.getString("TABLE_TYPE");
                String remarks = tableRs.getString("REMARKS");

                log.info("发现表: {}, 类型: {}", tableName, tableType);

                // 应用表过滤条件
                if (shouldSkipTable(tableName, includeTables, excludeTables)) {
                    log.info("跳过表: {}", tableName);
                    continue;
                }

                boolean isView = "VIEW".equalsIgnoreCase(tableType);

                if (isView) {
                    viewsTotal.incrementAndGet();
                    log.info("处理视图: {}, 当前视图总数: {}", tableName, viewsTotal.get());
                } else {
                    tablesTotal.incrementAndGet();
                    log.info("处理表: {}, 当前表总数: {}", tableName, tablesTotal.get());
                }

                // 创建Table记录
                String tableId = UUID.randomUUID().toString().replace("-", "");
                Table table = Table.builder()
                        .id(tableId)
                        .schemaId(schema.getId())
                        .datasourceId(schema.getDatasourceId())
                        .name(tableName)
                        .type(isView ? "VIEW" : "TABLE")
                        .description(remarks)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                tables.add(table);

                // 添加到新表列表
                log.info("添加表到差量更新列表: {}", table);

                // 添加到按schema分组的表集合
                // 使用schema的名称作为key，避免ID不一致的问题
                String schemaKey = schema.getName();
                List<Table> schemaTables = newTables.computeIfAbsent(schemaKey, k -> new ArrayList<>());
                schemaTables.add(table);

                // 初始化该表的列集合
                newColumns.put(table.getId(), new ArrayList<>());

                // 获取列信息
                try (ResultSet columnRs = dbMetaData.getColumns(
                        null, schemaName, tableName, "%")) {

                    int columnCount = 0;
                    while (columnRs.next()) {
                        columnCount++;
                        String columnName = columnRs.getString("COLUMN_NAME");
                        String dataType = columnRs.getString("TYPE_NAME");
                        int columnSize = columnRs.getInt("COLUMN_SIZE");
                        int position = columnRs.getInt("ORDINAL_POSITION");
                        String nullable = columnRs.getString("IS_NULLABLE");
                        String defaultValue = columnRs.getString("COLUMN_DEF");
                        String columnRemarks = columnRs.getString("REMARKS");

                        log.debug("处理列: {}, 类型: {}", columnName, dataType);

                        // 创建Column记录
                        String columnId = UUID.randomUUID().toString().replace("-", "");
                        Column column = Column.builder()
                                .id(columnId)
                                .tableId(tableId)
                                .name(columnName)
                                .dataType(dataType)
                                .columnType(dataType + "(" + columnSize + ")")
                                .position(position)
                                .isNullable("YES".equalsIgnoreCase(nullable))
                                .defaultValue(defaultValue)
                                .description(columnRemarks)
                                .characterLength(columnSize)
                                .isEncrypted(isEncryptedColumn(columnName, dataType + "(" + columnSize + ")", columnRemarks))
                                .createdAt(LocalDateTime.now())
                                .updatedAt(LocalDateTime.now())
                                .build();

                        // 添加到新列列表
                        List<Column> tableColumns = newColumns.get(tableId);
                        if (tableColumns != null) {
                            tableColumns.add(column);
                        }
                    }

                    // 更新表的列计数
                    log.info("表[{}]列数: {}", tableName, columnCount);
                    table.setColumnsCount(columnCount);
                }
            }

            // 更新模式的表计数
            log.info("Schema[{}]表总数: {}", schemaName, tables.size());
            schema.setTablesCount(tables.size());
        }
    }

    private void extractMySQLTablesWithCatalog(DatabaseMetaData dbMetaData, Schema schema, String catalogName,
                                              List<String> includeTables, List<String> excludeTables,
                                               AtomicInteger tablesTotal, AtomicInteger viewsTotal,
                                               Map<String, List<Table>> newTables, Map<String, List<Column>> newColumns) throws SQLException {
        log.info("开始提取catalog[{}]的表信息", catalogName);

        // 获取表信息
        List<Table> tables = new ArrayList<>();
        try (ResultSet tableRs = dbMetaData.getTables(
                catalogName, null, "%", new String[]{"TABLE", "VIEW"})) {

            while (tableRs.next()) {
                String tableName = tableRs.getString("TABLE_NAME");
                String tableType = tableRs.getString("TABLE_TYPE");
                String remarks = tableRs.getString("REMARKS");

                log.info("发现表: {}, 类型: {}", tableName, tableType);

                // 应用表过滤条件
                if (shouldSkipTable(tableName, includeTables, excludeTables)) {
                    log.info("跳过表: {}", tableName);
                    continue;
                }

                boolean isView = "VIEW".equalsIgnoreCase(tableType);

                if (isView) {
                    viewsTotal.incrementAndGet();
                    log.info("处理视图: {}, 当前视图总数: {}", tableName, viewsTotal.get());
                } else {
                    tablesTotal.incrementAndGet();
                    log.info("处理表: {}, 当前表总数: {}", tableName, tablesTotal.get());
                }

                // 创建Table记录
                String tableId = UUID.randomUUID().toString().replace("-", "");
                Table table = Table.builder()
                        .id(tableId)
                        .schemaId(schema.getId())
                        .datasourceId(schema.getDatasourceId())
                        .name(tableName)
                        .type(isView ? "VIEW" : "TABLE")
                        .description(remarks)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                tables.add(table);

                // 添加到新表列表
                log.info("添加表到差量更新列表: {}", table);

                // 添加到按schema分组的表集合
                // 使用schema的名称作为key，避免ID不一致的问题
                String schemaKey = schema.getName();
                List<Table> schemaTables = newTables.computeIfAbsent(schemaKey, k -> new ArrayList<>());
                schemaTables.add(table);

                // 初始化该表的列集合
                newColumns.put(table.getId(), new ArrayList<>());

                // 获取列信息
                try (ResultSet columnRs = dbMetaData.getColumns(
                        catalogName, null, tableName, "%")) {

                    int columnCount = 0;
                    while (columnRs.next()) {
                        columnCount++;
                        String columnName = columnRs.getString("COLUMN_NAME");
                        String dataType = columnRs.getString("TYPE_NAME");
                        int columnSize = columnRs.getInt("COLUMN_SIZE");
                        int position = columnRs.getInt("ORDINAL_POSITION");
                        String nullable = columnRs.getString("IS_NULLABLE");
                        String defaultValue = columnRs.getString("COLUMN_DEF");
                        String columnRemarks = columnRs.getString("REMARKS");

                        log.debug("处理列: {}, 类型: {}", columnName, dataType);

                        // 创建Column记录
                        String columnId = UUID.randomUUID().toString().replace("-", "");
                        Column column = Column.builder()
                                .id(columnId)
                                .tableId(tableId)
                                .name(columnName)
                                .dataType(dataType)
                                .columnType(dataType + "(" + columnSize + ")")
                                .position(position)
                                .isNullable("YES".equalsIgnoreCase(nullable))
                                .defaultValue(defaultValue)
                                .description(columnRemarks)
                                .characterLength(columnSize)
                                .isEncrypted(isEncryptedColumn(columnName, dataType + "(" + columnSize + ")", columnRemarks))
                                .createdAt(LocalDateTime.now())
                                .updatedAt(LocalDateTime.now())
                                .build();

                        // 添加到新列列表
                        List<Column> tableColumns = newColumns.get(tableId);
                        if (tableColumns != null) {
                            tableColumns.add(column);
                        }
                    }

                    // 更新表的列计数
                    log.info("表[{}]列数: {}", tableName, columnCount);
                    table.setColumnsCount(columnCount);
                }
            }

            // 更新模式的表计数
            log.info("Schema[{}]表总数: {}", catalogName, tables.size());
            schema.setTablesCount(tables.size());
        }
    }

    private void extractMySQLTablesWithoutCatalog(DatabaseMetaData dbMetaData, Schema schema,
                                              List<String> includeTables, List<String> excludeTables,
                                                  AtomicInteger tablesTotal, AtomicInteger viewsTotal,
                                                  Map<String, List<Table>> newTables, Map<String, List<Column>> newColumns) throws SQLException {
        log.info("开始提取不指定catalog的表信息");

        // 获取表信息
        List<Table> tables = new ArrayList<>();
        try (ResultSet tableRs = dbMetaData.getTables(
                null, null, "%", new String[]{"TABLE", "VIEW"})) {

            while (tableRs.next()) {
                String tableName = tableRs.getString("TABLE_NAME");
                String tableType = tableRs.getString("TABLE_TYPE");
                String remarks = tableRs.getString("REMARKS");

                log.info("发现表: {}, 类型: {}", tableName, tableType);

                // 应用表过滤条件
                if (shouldSkipTable(tableName, includeTables, excludeTables)) {
                    log.info("跳过表: {}", tableName);
                    continue;
                }

                boolean isView = "VIEW".equalsIgnoreCase(tableType);

                if (isView) {
                    viewsTotal.incrementAndGet();
                    log.info("处理视图: {}, 当前视图总数: {}", tableName, viewsTotal.get());
                } else {
                    tablesTotal.incrementAndGet();
                    log.info("处理表: {}, 当前表总数: {}", tableName, tablesTotal.get());
                }

                // 创建Table记录
                String tableId = UUID.randomUUID().toString().replace("-", "");
                Table table = Table.builder()
                        .id(tableId)
                        .schemaId(schema.getId())
                        .datasourceId(schema.getDatasourceId())
                        .name(tableName)
                        .type(isView ? "VIEW" : "TABLE")
                        .description(remarks)
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                tables.add(table);

                // 添加到新表列表
                log.info("添加表到差量更新列表: {}", table);

                // 添加到按schema分组的表集合
                // 使用schema的名称作为key，避免ID不一致的问题
                String schemaKey = schema.getName();
                List<Table> schemaTables = newTables.computeIfAbsent(schemaKey, k -> new ArrayList<>());
                schemaTables.add(table);

                // 初始化该表的列集合
                newColumns.put(table.getId(), new ArrayList<>());

                // 获取列信息
                try (ResultSet columnRs = dbMetaData.getColumns(
                        null, null, tableName, "%")) {

                    int columnCount = 0;
                    while (columnRs.next()) {
                        columnCount++;
                        String columnName = columnRs.getString("COLUMN_NAME");
                        String dataType = columnRs.getString("TYPE_NAME");
                        int columnSize = columnRs.getInt("COLUMN_SIZE");
                        int position = columnRs.getInt("ORDINAL_POSITION");
                        String nullable = columnRs.getString("IS_NULLABLE");
                        String defaultValue = columnRs.getString("COLUMN_DEF");
                        String columnRemarks = columnRs.getString("REMARKS");

                        log.debug("处理列: {}, 类型: {}", columnName, dataType);

                        // 创建Column记录
                        String columnId = UUID.randomUUID().toString().replace("-", "");
                        Column column = Column.builder()
                                .id(columnId)
                                .tableId(tableId)
                                .name(columnName)
                                .dataType(dataType)
                                .columnType(dataType + "(" + columnSize + ")")
                                .position(position)
                                .isNullable("YES".equalsIgnoreCase(nullable))
                                .defaultValue(defaultValue)
                                .description(columnRemarks)
                                .characterLength(columnSize)
                                .isEncrypted(isEncryptedColumn(columnName, dataType + "(" + columnSize + ")", columnRemarks))
                                .createdAt(LocalDateTime.now())
                                .updatedAt(LocalDateTime.now())
                                .build();

                        // 添加到新列列表
                        List<Column> tableColumns = newColumns.get(tableId);
                        if (tableColumns != null) {
                            tableColumns.add(column);
                        }
                    }

                    // 更新表的列计数
                    log.info("表[{}]列数: {}", tableName, columnCount);
                    table.setColumnsCount(columnCount);
                }
            }

            // 更新模式的表计数
            log.info("Schema[{}]表总数: {}", "不指定catalog", tables.size());
            schema.setTablesCount(tables.size());
        }
    }

    /**
     * 判断是否是加密列
     * @param columnName 列名
     * @param columnType 列类型
     * @param description 列描述
     * @return 是否是加密列
     */
    private boolean isEncryptedColumn(String columnName, String columnType, String description) {
        // 1. 根据列名判断
        String lowerColumnName = columnName.toLowerCase();
        if (lowerColumnName.contains("encrypt") ||
            lowerColumnName.contains("password") ||
            lowerColumnName.contains("secret") ||
            lowerColumnName.contains("key") ||
            lowerColumnName.contains("token") ||
            lowerColumnName.contains("hash") ||
            lowerColumnName.contains("salt") ||
            lowerColumnName.contains("pwd") ||
            lowerColumnName.contains("cipher")) {
            return true;
        }

        // 2. 根据列描述判断
        if (description != null) {
            String lowerDescription = description.toLowerCase();
            if (lowerDescription.contains("encrypt") ||
                lowerDescription.contains("password") ||
                lowerDescription.contains("secret") ||
                lowerDescription.contains("加密") ||
                lowerDescription.contains("密码")) {
                return true;
            }
        }

        // 3. 根据列类型判断
        // 如果是二进制类型，可能存储加密数据
        if (columnType != null) {
            String lowerColumnType = columnType.toLowerCase();
            if (lowerColumnType.contains("binary") ||
                lowerColumnType.contains("blob") ||
                lowerColumnType.contains("bytea")) {
                return true;
            }
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColumnEntry(String columnId, Object param) {
        log.info("updateColumnData columnId: {}，param: {}", columnId, param);
        if (Objects.isNull(param)) {
            return;
        }
        String entry;
        try {
            entry = objectMapper.writeValueAsString(param);
            JsonNode jsonNode = objectMapper.readTree(entry);
            if (jsonNode.isEmpty()) {
                return;
            }

            LambdaUpdateWrapper<Column> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(Column::getId, columnId);

            // 更新解密配置
            wrapper.set(Column::getEncryConfig, entry);

            // 根据配置内容判断是否启用解密
            boolean isEncrypted = false;
            if (jsonNode.has("aes") || jsonNode.has("gm")) {
                isEncrypted = true;
            }
            wrapper.set(Column::getIsEncrypted, isEncrypted);

            this.columnMapper.update(null, wrapper);
        } catch (JsonProcessingException e) {
            log.error("parse param error", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColumnDecryptConfig(String columnId, Object decryptConfig) {
        log.info("更新列解密配置 columnId: {}，decryptConfig: {}", columnId, decryptConfig);
        if (Objects.isNull(decryptConfig)) {
            return;
        }

        String configJson;
        try {
            configJson = objectMapper.writeValueAsString(decryptConfig);
            JsonNode jsonNode = objectMapper.readTree(configJson);

            LambdaUpdateWrapper<Column> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(Column::getId, columnId);

            // 更新解密配置
            wrapper.set(Column::getEncryConfig, configJson);

            // 根据配置内容判断是否启用解密
            boolean isEncrypted = false;
            if (jsonNode.has("aes") || jsonNode.has("gm")) {
                isEncrypted = true;
            }
            wrapper.set(Column::getIsEncrypted, isEncrypted);

            this.columnMapper.update(null, wrapper);
            log.info("更新列[{}]解密配置成功", columnId);
        } catch (JsonProcessingException e) {
            log.error("解析解密配置参数失败", e);
            throw new RuntimeException("解析解密配置参数失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authMetaData(AuthDTO authDTO) {
        if (Objects.isNull(authDTO)) {
            return;
        }

        // 参数验证
        Assert.isTrue(StrUtil.isNotBlank(authDTO.getType()), () -> new RuntimeException("授权类型不能为空"));
        Assert.isTrue(StrUtil.isNotBlank(authDTO.getId()), () -> new RuntimeException("授权ID不能为空"));

        try {
            // 记录授权操作开始
            log.info("开始处理授权配置: 类型={}, ID={}, 授权状态={}",
                    authDTO.getType(), authDTO.getId(), authDTO.getAuthRequired());

            // 获取对应的授权处理器
            AbstractAuthCenter authCenter = authFactory.getAuth(authDTO.getType());

            // 处理授权配置
            authCenter.process(authDTO);

            // 记录授权操作成功
            log.info("授权配置处理成功: 类型={}, ID={}, 授权状态={}",
                    authDTO.getType(), authDTO.getId(), authDTO.getAuthRequired());
        } catch (RuntimeException e) {
            // 记录详细错误信息
            log.error("授权配置处理失败: 类型={}, ID={}, 授权状态={}, 错误信息={}",
                    authDTO.getType(), authDTO.getId(), authDTO.getAuthRequired(), e.getMessage(), e);

            // 转换为业务异常，提供更友好的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage.contains("call add resource failed") ||
                errorMessage.contains("调用授权服务失败")) {
                throw new BusinessException("授权服务连接失败，请稍后重试或联系管理员");
            } else {
                throw new BusinessException("授权配置保存失败: " + errorMessage);
            }
        } catch (Exception e) {
            // 处理其他未预期的异常
            log.error("授权配置处理发生未预期异常: 类型={}, ID={}, 授权状态={}",
                    authDTO.getType(), authDTO.getId(), authDTO.getAuthRequired(), e);
            throw new BusinessException("授权配置保存失败，系统内部错误");
        }
    }


}
