package com.datascope.app.service;

import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.model.ResourceBO;
import com.datascope.app.model.RoleBO;
import com.datascope.app.model.RoleResourceBindBO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AuthService {

    /**
     * 添加/删除资源
     *
     * @param resourceBO resourceBO
     * @param path path
     */
    void callAddOrDelResource(ResourceBO resourceBO, String path);

    /**
     * 添加角色
     *
     * @param roleBO roleBO
     */
    void callAddRole(RoleBO roleBO);

    /**
     * 绑定角色和资源
     *
     * @param roleResourceBindBO roleResourceBindBO
     * @param roleCode 角色编码
     */
    void callBindRoleResource(RoleResourceBindBO roleResourceBindBO, String roleCode);

    /**
     * 根据资源编码创建角色并绑定资源（便捷方法）
     *
     * @param resourceCode 资源编码
     */
    void createRoleAndBindResource(String resourceCode);

    /**
     * 获取资源
     *
     * @param path path
     * @param token token
     * @return List
     */
    List<AuthResourceBO> getResources(String path, String token);

    /**
     * checkAuth
     *
     * @param path path
     * @param loginName loginName
     * @param token token
     * @return boolean
     */
    boolean checkAuth(String path, String loginName, String token);

    /**
     * checkSqlAuth
     *
     * @param authDTO authDTO
     */
    void checkSqlAuth(AuthDTO authDTO);

    /**
     * getAuthToken
     *
     * @return String
     */
    String getAuthToken();
}
