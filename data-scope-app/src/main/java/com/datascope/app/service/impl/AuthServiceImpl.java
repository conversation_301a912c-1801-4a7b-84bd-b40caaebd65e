package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.entity.Column;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.entity.Table;
import com.datascope.app.mapper.ColumnMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.mapper.TableMapper;
import com.datascope.app.model.AuthResourceBO;
import com.datascope.app.model.ResourceBO;
import com.datascope.app.model.RoleBO;
import com.datascope.app.model.RoleResourceBindBO;
import com.datascope.app.service.AuthService;
import com.datascope.app.util.SqlParserUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private TableMapper tableMapper;

    @Autowired
    private SchemaMapper schemaMapper;

    @Autowired
    private ColumnMapper columnMapper;

    @Autowired
    private ThreadPoolExecutor resourceExecutor;

    @Value("${auth.call.url}")
    private String callUrl;

    @Value("${auth.signature}")
    private String signature;

    @Override
    public String getAuthToken() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        return request.getHeader("Authorization");
    }

    private HttpHeaders createHeader(String token) {
        //创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("systemcode", "data-scope");
        headers.add("expire-time", "1800");
        headers.add("request-uuid", UUID.randomUUID().toString());
        headers.add("signature", signature);
        headers.add("timestamp", System.currentTimeMillis() + "");
        headers.add("Authorization", token);
        return headers;
    }

    @Override
    public void callAddOrDelResource(ResourceBO resourceBO, String path) {
        String jsonString;
        try {
            // 记录详细的请求信息
            log.info("准备调用授权服务，资源信息: {}, 路径: {}, 授权服务URL: {}", resourceBO, path, callUrl);

            jsonString = objectMapper.writeValueAsString(resourceBO);
            log.debug("授权请求JSON: {}", jsonString);

            // 获取授权Token并记录
            String authToken = getAuthToken();
            log.debug("授权Token: {}", authToken != null ? (authToken.length() > 10 ? authToken.substring(0, 10) + "..." : authToken) : "null");

            HttpEntity<String> entity = new HttpEntity<>(jsonString, createHeader(authToken));

            // 记录完整请求URL
            String requestUrl = callUrl + path;
            log.info("发送授权请求到: {}", requestUrl);

            try {
                ResponseEntity<String> responseEntity = restTemplate
                    .postForEntity(requestUrl, entity, String.class);

                // 记录响应状态和内容
                String body = responseEntity.getBody();
                log.info("授权服务响应状态: {}, 响应内容: {}", responseEntity.getStatusCode(), body);

                if (body == null) {
                    log.error("授权服务返回空响应");
                    throw new RuntimeException("授权服务返回空响应");
                }

                JsonNode jsonNode = objectMapper.readTree(body);

                // 检查响应中的code字段
                if (!jsonNode.has(Constant.CODE)) {
                    log.error("授权服务响应缺少code字段: {}", body);
                    throw new RuntimeException("授权服务响应格式错误: 缺少code字段");
                }

                int code = jsonNode.get(Constant.CODE).asInt();
                if (HttpStatus.OK.value() != code) {
                    // 尝试提取更详细的错误信息
                    String message = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    log.error("授权服务返回错误码: {}, 错误信息: {}, 资源信息: {}, 路径: {}",
                            code, message, resourceBO, path);

                    // 针对删除资源时的特殊处理
                    if (path.contains(Constant.UrlPath.DEL_RESOURCE)) {
                        // 如果是删除资源时出错，可能是资源不存在，这种情况可以忽略错误
                        if (message.contains("暂未查到") || message.contains("不存在") || code == 404) {
                            log.warn("删除不存在的授权资源，忽略错误: 资源标识={}", resourceBO.getResourceCode());
                            return; // 直接返回，不抛出异常
                        }
                    }

                    if (path.contains(Constant.UrlPath.ADD_RESOURCE)) {
                        // 如果是添加资源时出错，可能是资源已存在，这种情况可以忽略错误
                        if (message.contains("同一系统下资源代码重复") || code == 400) {
                            log.warn("添加授权资源，已经存在: 资源标识={}", resourceBO.getResourceCode());
                            return;
                        }
                    }

                    throw new RuntimeException("调用授权服务失败: " + message);
                }

                // 如果是添加资源操作且成功，则自动创建角色并绑定资源
                if (path.contains(Constant.UrlPath.ADD_RESOURCE)) {
                    try {
                        log.info("资源添加成功，开始自动创建角色并绑定资源: {}", resourceBO.getResourceCode());
                        createRoleAndBindResource(resourceBO.getResourceCode());
                        log.info("自动创建角色并绑定资源成功: {}", resourceBO.getResourceCode());
                    } catch (Exception e) {
                        // 角色创建失败不影响资源添加的结果，只记录错误日志
                        log.error("自动创建角色并绑定资源失败，但资源添加成功: resourceCode={}",
                                resourceBO.getResourceCode(), e);
                        // 不抛出异常，因为主要的资源添加操作已经成功
                    }
                }

            } catch (RestClientException e) {
                // 捕获RestTemplate可能抛出的异常
                log.error("调用授权服务网络错误: {}", e.getMessage(), e);
                throw new RuntimeException("调用授权服务网络错误: " + e.getMessage(), e);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON处理错误", e);
            throw new RuntimeException("JSON处理错误: " + e.getMessage(), e);
        } catch (RuntimeException e) {
            // 重新抛出已经处理过的RuntimeException
            throw e;
        } catch (Exception e) {
            // 捕获其他可能的异常
            log.error("调用授权服务未预期错误", e);
            throw new RuntimeException("调用授权服务未预期错误: " + e.getMessage(), e);
        }
    }

    @Override
    public void callAddRole(RoleBO roleBO) {
        String jsonString;
        try {
            // 记录详细的请求信息
            log.info("准备调用角色创建服务，角色信息: {}, 授权服务URL: {}", roleBO, callUrl);

            jsonString = objectMapper.writeValueAsString(roleBO);
            log.debug("角色创建请求JSON: {}", jsonString);

            // 获取授权Token并记录
            String authToken = getAuthToken();
            log.debug("授权Token: {}", authToken != null ? (authToken.length() > 10 ? authToken.substring(0, 10) + "..." : authToken) : "null");

            HttpEntity<String> entity = new HttpEntity<>(jsonString, createHeader(authToken));

            // 记录完整请求URL
            String requestUrl = callUrl + Constant.UrlPath.ADD_ROLE;
            log.info("发送角色创建请求到: {}", requestUrl);

            try {
                ResponseEntity<String> responseEntity = restTemplate
                    .postForEntity(requestUrl, entity, String.class);

                // 记录响应状态和内容
                String body = responseEntity.getBody();
                log.info("角色创建服务响应状态: {}, 响应内容: {}", responseEntity.getStatusCode(), body);

                if (body == null) {
                    log.error("角色创建服务返回空响应");
                    throw new RuntimeException("角色创建服务返回空响应");
                }

                JsonNode jsonNode = objectMapper.readTree(body);

                // 检查响应中的code字段
                if (!jsonNode.has(Constant.CODE)) {
                    log.error("角色创建服务响应缺少code字段: {}", body);
                    throw new RuntimeException("角色创建服务响应格式错误: 缺少code字段");
                }

                int code = jsonNode.get(Constant.CODE).asInt();
                if (HttpStatus.OK.value() != code) {
                    // 尝试提取更详细的错误信息
                    String message = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    log.error("角色创建服务返回错误码: {}, 错误信息: {}, 角色信息: {}",
                            code, message, roleBO);

                    throw new RuntimeException("调用角色创建服务失败: " + message);
                }
            } catch (RestClientException e) {
                // 捕获RestTemplate可能抛出的异常
                log.error("调用角色创建服务网络错误: {}", e.getMessage(), e);
                throw new RuntimeException("调用角色创建服务网络错误: " + e.getMessage(), e);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON处理错误", e);
            throw new RuntimeException("JSON处理错误: " + e.getMessage(), e);
        } catch (RuntimeException e) {
            // 重新抛出已经处理过的RuntimeException
            throw e;
        } catch (Exception e) {
            // 捕获其他可能的异常
            log.error("调用角色创建服务未预期错误", e);
            throw new RuntimeException("调用角色创建服务未预期错误: " + e.getMessage(), e);
        }
    }

    @Override
    public void callBindRoleResource(RoleResourceBindBO roleResourceBindBO, String roleCode) {
        String jsonString;
        try {
            // 记录详细的请求信息
            log.info("准备调用角色资源绑定服务，绑定信息: {}, 角色编码: {}, 授权服务URL: {}", roleResourceBindBO, roleCode, callUrl);

            jsonString = objectMapper.writeValueAsString(roleResourceBindBO);
            log.debug("角色资源绑定请求JSON: {}", jsonString);

            // 获取授权Token并记录
            String authToken = getAuthToken();
            log.debug("授权Token: {}", authToken != null ? (authToken.length() > 10 ? authToken.substring(0, 10) + "..." : authToken) : "null");

            HttpEntity<String> entity = new HttpEntity<>(jsonString, createHeader(authToken));

            // 记录完整请求URL
            String requestUrl = callUrl + "/meta/save_resources/" + roleCode;
            log.info("发送角色资源绑定请求到: {}", requestUrl);

            try {
                ResponseEntity<String> responseEntity = restTemplate
                    .postForEntity(requestUrl, entity, String.class);

                // 记录响应状态和内容
                String body = responseEntity.getBody();
                log.info("角色资源绑定服务响应状态: {}, 响应内容: {}", responseEntity.getStatusCode(), body);

                if (body == null) {
                    log.error("角色资源绑定服务返回空响应");
                    throw new RuntimeException("角色资源绑定服务返回空响应");
                }

                JsonNode jsonNode = objectMapper.readTree(body);

                // 检查响应中的code字段
                if (!jsonNode.has(Constant.CODE)) {
                    log.error("角色资源绑定服务响应缺少code字段: {}", body);
                    throw new RuntimeException("角色资源绑定服务响应格式错误: 缺少code字段");
                }

                int code = jsonNode.get(Constant.CODE).asInt();
                if (HttpStatus.OK.value() != code) {
                    // 尝试提取更详细的错误信息
                    String message = jsonNode.has("message") ? jsonNode.get("message").asText() : "未知错误";
                    log.error("角色资源绑定服务返回错误码: {}, 错误信息: {}, 绑定信息: {}, 角色编码: {}",
                            code, message, roleResourceBindBO, roleCode);

                    throw new RuntimeException("调用角色资源绑定服务失败: " + message);
                }
            } catch (RestClientException e) {
                // 捕获RestTemplate可能抛出的异常
                log.error("调用角色资源绑定服务网络错误: {}", e.getMessage(), e);
                throw new RuntimeException("调用角色资源绑定服务网络错误: " + e.getMessage(), e);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON处理错误", e);
            throw new RuntimeException("JSON处理错误: " + e.getMessage(), e);
        } catch (RuntimeException e) {
            // 重新抛出已经处理过的RuntimeException
            throw e;
        } catch (Exception e) {
            // 捕获其他可能的异常
            log.error("调用角色资源绑定服务未预期错误", e);
            throw new RuntimeException("调用角色资源绑定服务未预期错误: " + e.getMessage(), e);
        }
    }

    @Override
    public void createRoleAndBindResource(String resourceCode) {
        try {
            log.info("开始为资源编码 {} 创建角色并绑定资源", resourceCode);

            // 1. 构建角色编码：role: + resourceCode
            String roleCode = "role:" + resourceCode;

            // 2. 创建角色
            RoleBO roleBO = new RoleBO()
                .setRoleCode(roleCode)
                .setRoleName(roleCode)
                .setResourceStatus("enabled")
                .setSystemCode("data-scope");

            log.info("创建角色: {}", roleBO);
            callAddRole(roleBO);
            log.info("角色创建成功: {}", roleCode);

            // 3. 绑定角色和资源
            RoleResourceBindBO bindBO = new RoleResourceBindBO()
                .setSystemCode("data-scope")
                .setResourceCode(Lists.newArrayList(resourceCode));

            log.info("绑定角色 {} 和资源: {}", roleCode, bindBO);
            callBindRoleResource(bindBO, roleCode);
            log.info("角色资源绑定成功: 角色={}, 资源={}", roleCode, resourceCode);

        } catch (Exception e) {
            log.error("为资源编码 {} 创建角色并绑定资源失败", resourceCode, e);
            throw new RuntimeException("创建角色并绑定资源失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AuthResourceBO> getResources(String path, String token) {
        HttpEntity<String> entity = new HttpEntity<>("{}", createHeader(token));
        ResponseEntity<String> exchange = restTemplate
            .exchange(callUrl + "/meta/get_users_by_resources/" + path, HttpMethod.GET, entity, String.class);
        String body = exchange.getBody();
        log.info("getResources response: {}", body);
        try {
            if (StrUtil.isNotBlank(body)) {
                JsonNode jsonNode = objectMapper.readTree(body);
                if (!jsonNode.has(Constant.CODE) || HttpStatus.OK.value() != jsonNode.get(Constant.CODE).asInt()) {
                    throw new RuntimeException("call getResources failed");
                }
                JsonNode value = jsonNode.findValue(Constant.DATA);
                if (Objects.nonNull(value) && !value.isEmpty()) {
                    return objectMapper.readerForListOf(AuthResourceBO.class).readValue(value);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean checkAuth(String path, String loginName, String token) {
        List<AuthResourceBO> resources = getResources(path, token);
        if (CollUtil.isEmpty(resources)) {
            return false;
        }
        return resources.stream().anyMatch(resource -> resource.getLoginName().equals(loginName));
    }

    @Override
    public void checkSqlAuth(AuthDTO authDTO) {
        if (Objects.isNull(authDTO) || StrUtil.isEmpty(authDTO.getSql()) || StrUtil.isEmpty(authDTO.getLoginName())) {
            log.warn("checkSqlAuth param empty, skip...");
            return;
        }
        String sql = authDTO.getSql();
        log.info("checkSqlAuth sql: {}", sql);
        Datasource datasource = authDTO.getDatasource();
        Assert.isTrue(datasource != null, "checkSqlAuth datasource is null");

        String schemaName = datasource.getSchema();
        if (StrUtil.isBlank(schemaName)) {
            schemaName = datasource.getDatabaseName();
        }
        Schema schema = getSchema(datasource.getId(), schemaName);
        Assert.isTrue(schema != null, "checkSqlAuth schema is null");
        assert schema != null;
        if (schema.getIsAuthRequired()) {
            boolean flag = this.checkAuth(datasource.getDatabaseName()
                + StrPool.COLON + schema.getName(), authDTO.getLoginName(), this.getAuthToken());
            Assert.isTrue(flag, () -> new RuntimeException("您没权限操作schema: " + schema.getName()));
        }

        Map<String, Set<String>> sqlParseMap = SqlParserUtil.extractTableAndColumns(sql);
        log.info("checkSqlAuth sqlParseMap: {}", sqlParseMap);
        Set<String> tableNames = sqlParseMap.keySet();
        log.info("checkSqlAuth extract tables: {}", tableNames);
        List<Table> tables = getTables(datasource, schema.getId(), tableNames);
        List<Table> authTables = tables.stream().filter(Table::getIsAuthRequired).collect(Collectors.toList());
        log.info("checkSqlAuth authTables tables: {}", authTables);
        if (CollUtil.isNotEmpty(authTables)) {
            List<String> noPermissionTable = Lists.newArrayList();
            for (Table authTable : authTables) {
                String resourceCode = datasource.getDatabaseName() + StrPool.COLON
                    + schema.getName() + StrPool.COLON + authTable.getName();
                boolean flag = this.checkAuth(resourceCode, authDTO.getLoginName(), this.getAuthToken());
                if (!flag) {
                    noPermissionTable.add(authTable.getName());
                }
            }
            log.info("checkSqlAuth noPermissionTable: {}", noPermissionTable);
            Assert.isTrue(CollUtil.isEmpty(noPermissionTable),  () -> new RuntimeException("您没权限操作表: " + noPermissionTable));
        }

        // check field
        List<Pair<String, String>> resourceCodes = getResourceCodes(datasource, schema, sqlParseMap, tables);
        try {
            String resource = objectMapper.writeValueAsString(resourceCodes);
            log.info("checkSqlAuth request resourceCodes: {}", resource);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (CollUtil.isNotEmpty(resourceCodes)) {
            List<String> noPermissionField = checkFieldPermission(resourceCodes, authDTO.getLoginName());
            log.info("checkSqlAuth checkFieldPermissions noPermissionField: {}", noPermissionField);
            Assert.isTrue(CollUtil.isEmpty(noPermissionField),  () -> new RuntimeException("您没权限操作表字段: " + noPermissionField));
        }
    }

    private List<String> checkFieldPermission(List<Pair<String, String>> resourceCodes, String loginName) {
        CountDownLatch countDownLatch = new CountDownLatch(resourceCodes.size());
        List<String> synchronizedList = Collections.synchronizedList(new ArrayList<>());
        String authToken = this.getAuthToken();
        resourceCodes.forEach(pair ->
            resourceExecutor.execute(() -> {
                try {
                    boolean flag = this.checkAuth(pair.getValue(), loginName, authToken);
                    if (!flag) {
                        synchronizedList.add(pair.getKey());
                    }
                } catch (Exception e) {
                    log.error("checkFieldPermission error field: {}", pair.getValue(), e);
                } finally {countDownLatch.countDown();
                }
            }));
        try {
            countDownLatch.await(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("checkFieldPermission countDownLatch interrupted: ", e);
        }
        return synchronizedList;
    }

    private List<Pair<String, String>> getResourceCodes(Datasource datasource, Schema schema,
                                        Map<String, Set<String>> sqlParseMap,
                                        List<Table> tables) {
        List<Pair<String, String>> resourceCodes = Lists.newArrayList();
        for (Map.Entry<String, Set<String>> entry : sqlParseMap.entrySet()) {
            String key = entry.getKey();
            tables.stream().filter(table -> table.getName().equals(key)).findFirst()
                .ifPresent(table -> {
                    String tableId = table.getId();
                    List<Column> columns = getColumns(tableId, entry.getValue());
                    if (CollUtil.isNotEmpty(columns)) {
                        String resourceCode = datasource.getDatabaseName() + StrPool.COLON
                            + schema.getName() + StrPool.COLON + table.getName();
                        columns.forEach(column ->
                            resourceCodes.add(Pair.of(table.getName() + StrPool.DOT + column.getName(),
                                resourceCode + StrPool.COLON + column.getName())));
                    }
                });
        }
        return resourceCodes;
    }

    private List<Table> getTables(Datasource datasource, String schemaId, Set<String> tableNames) {
        LambdaQueryWrapper<Table> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Table::getDatasourceId, datasource.getId()).eq(Table::getSchemaId, schemaId);
        wrapper.in(Table::getName, tableNames);
        return tableMapper.selectList(wrapper);
    }

    private Schema getSchema(String datasourceId, String name) {
        LambdaQueryWrapper<Schema> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Schema::getDatasourceId, datasourceId).eq(Schema::getName, name);
        return schemaMapper.selectOne(wrapper);
    }

    private List<Column> getColumns(String tableId, Set<String> columns) {
        LambdaQueryWrapper<Column> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Column::getTableId, tableId);
        if (CollUtil.isNotEmpty(columns)) {
           if (columns.contains("*")) {
               columns = Sets.newHashSet();
           }
        }
        wrapper.in(CollUtil.isNotEmpty(columns), Column::getName, columns);
        return columnMapper.selectList(wrapper).stream().filter(Column::getIsAuthRequired).collect(Collectors.toList());
    }
}
