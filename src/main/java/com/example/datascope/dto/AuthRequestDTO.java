package com.example.datascope.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 权限申请DTO
 */
@Data
public class AuthRequestDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 资源类型：DATASOURCE, SCHEMA, TABLE, COLUMN
     */
    @NotBlank(message = "资源类型不能为空")
    private String resourceType;

    /**
     * 资源ID
     */
    @NotBlank(message = "资源ID不能为空")
    private String resourceId;

    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    private String resourceName;

    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    private String reason;

    /**
     * 申请用户ID
     */
    private String userId;

    /**
     * 申请用户名
     */
    private String userName;

    /**
     * 申请状态：PENDING, APPROVED, REJECTED, EXPIRED
     */
    private String status;

    /**
     * 申请时间
     */
    private LocalDateTime requestTime;

    /**
     * 审批时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审批人
     */
    private String approvedBy;

    /**
     * 拒绝理由
     */
    private String rejectionReason;

    /**
     * 权限过期时间
     */
    private LocalDateTime expiryTime;
}


