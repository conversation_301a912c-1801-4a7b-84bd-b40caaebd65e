package com.example.datascope.dto;

import lombok.Data;

/**
 * 用户权限状态DTO
 */
@Data
public class UserPermissionStatusDTO {

    /**
     * 是否有权限
     */
    private Boolean hasPermission;

    /**
     * 申请状态
     */
    private String requestStatus;

    /**
     * 申请ID
     */
    private String requestId;

    /**
     * 申请时间
     */
    private String requestTime;

    /**
     * 审批时间
     */
    private String approvalTime;

    /**
     * 过期时间
     */
    private String expiryTime;

    /**
     * 申请理由
     */
    private String reason;
}
