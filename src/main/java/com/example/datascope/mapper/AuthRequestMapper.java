package com.example.datascope.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.datascope.entity.AuthRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限申请Mapper接口
 */
@Mapper
public interface AuthRequestMapper extends BaseMapper<AuthRequest> {

    /**
     * 分页查询用户的权限申请
     */
    IPage<AuthRequest> selectUserAuthRequests(
            Page<AuthRequest> page,
            @Param("userId") String userId,
            @Param("status") String status,
            @Param("resourceType") String resourceType
    );

    /**
     * 查询用户对特定资源的权限申请
     */
    AuthRequest selectUserResourceRequest(
            @Param("userId") String userId,
            @Param("resourceType") String resourceType,
            @Param("resourceId") String resourceId
    );

    /**
     * 查询用户对特定资源的有效权限
     */
    List<AuthRequest> selectUserValidPermissions(
            @Param("userId") String userId,
            @Param("resourceType") String resourceType,
            @Param("resourceId") String resourceId
    );

    /**
     * 分页查询所有权限申请（管理员用）
     */
    IPage<AuthRequest> selectAllAuthRequests(
            Page<AuthRequest> page,
            @Param("status") String status,
            @Param("resourceType") String resourceType,
            @Param("userId") String userId
    );
}
