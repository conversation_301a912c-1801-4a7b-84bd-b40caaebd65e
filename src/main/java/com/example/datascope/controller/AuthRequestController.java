package com.example.datascope.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.datascope.dto.AuthRequestDTO;
import com.example.datascope.dto.UserPermissionStatusDTO;
import com.example.datascope.entity.AuthRequest;
import com.example.datascope.service.AuthRequestService;
import com.example.datascope.utils.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 权限申请控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/auth/permissions")
@RequiredArgsConstructor
@Tag(name = "权限申请管理", description = "权限申请相关接口")
public class AuthRequestController {

    private final AuthRequestService authRequestService;

    @GetMapping("/status")
    @Operation(summary = "查询用户权限状态", description = "查询用户对特定资源的权限状态")
    public Result<UserPermissionStatusDTO> getUserPermissionStatus(
            @Parameter(description = "资源类型") @RequestParam String resourceType,
            @Parameter(description = "资源ID") @RequestParam String resourceId,
            HttpServletRequest request) {
        try {
            // 从请求中获取用户信息（这里简化处理，实际应该从认证信息中获取）
            String userId = getCurrentUserId(request);
            
            UserPermissionStatusDTO status = authRequestService.getUserPermissionStatus(userId, resourceType, resourceId);
            return Result.success(status);
        } catch (Exception e) {
            log.error("查询用户权限状态失败", e);
            return Result.error("查询权限状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/request")
    @Operation(summary = "提交权限申请", description = "用户提交权限申请")
    public Result<AuthRequest> requestPermission(
            @Valid @RequestBody AuthRequestDTO requestDTO,
            HttpServletRequest request) {
        try {
            // 从请求中获取用户信息
            String userId = getCurrentUserId(request);
            String userName = getCurrentUserName(request);
            
            AuthRequest authRequest = authRequestService.submitRequest(requestDTO, userId, userName);
            return Result.success(authRequest);
        } catch (Exception e) {
            log.error("提交权限申请失败", e);
            return Result.error("提交权限申请失败: " + e.getMessage());
        }
    }

    @GetMapping("/requests")
    @Operation(summary = "查询用户权限申请列表", description = "分页查询用户的权限申请")
    public Result<IPage<AuthRequest>> getUserAuthRequests(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "申请状态") @RequestParam(required = false) String status,
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType,
            HttpServletRequest request) {
        try {
            String userId = getCurrentUserId(request);
            IPage<AuthRequest> result = authRequestService.getUserAuthRequests(userId, page, size, status, resourceType);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询用户权限申请列表失败", e);
            return Result.error("查询权限申请列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/admin/requests")
    @Operation(summary = "查询所有权限申请", description = "管理员查询所有权限申请")
    public Result<IPage<AuthRequest>> getAllAuthRequests(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size,
            @Parameter(description = "申请状态") @RequestParam(required = false) String status,
            @Parameter(description = "资源类型") @RequestParam(required = false) String resourceType,
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId,
            HttpServletRequest request) {
        try {
            IPage<AuthRequest> result = authRequestService.getAllAuthRequests(page, size, status, resourceType, userId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("查询所有权限申请失败", e);
            return Result.error("查询权限申请失败: " + e.getMessage());
        }
    }

    @PostMapping("/admin/approve/{requestId}")
    @Operation(summary = "审批权限申请", description = "管理员审批权限申请")
    public Result<AuthRequest> approveRequest(
            @Parameter(description = "申请ID") @PathVariable Long requestId,
            @Parameter(description = "操作") @RequestParam String action,
            @Parameter(description = "拒绝理由") @RequestParam(required = false) String rejectionReason,
            HttpServletRequest request) {
        try {
            String approvedBy = getCurrentUserName(request);
            AuthRequest result = authRequestService.approveRequest(requestId, approvedBy, action, rejectionReason);
            return Result.success(result);
        } catch (Exception e) {
            log.error("审批权限申请失败", e);
            return Result.error("审批权限申请失败: " + e.getMessage());
        }
    }

    @GetMapping("/check")
    @Operation(summary = "检查用户权限", description = "检查用户是否有特定资源的权限")
    public Result<Boolean> checkPermission(
            @Parameter(description = "资源类型") @RequestParam String resourceType,
            @Parameter(description = "资源ID") @RequestParam String resourceId,
            HttpServletRequest request) {
        try {
            String userId = getCurrentUserId(request);
            boolean hasPermission = authRequestService.hasPermission(userId, resourceType, resourceId);
            return Result.success(hasPermission);
        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return Result.error("检查权限失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/revoke/{requestId}")
    @Operation(summary = "撤销权限申请", description = "用户撤销自己的权限申请")
    public Result<Void> revokeRequest(
            @Parameter(description = "申请ID") @PathVariable Long requestId,
            HttpServletRequest request) {
        try {
            String userId = getCurrentUserId(request);
            authRequestService.revokeRequest(requestId, userId);
            return Result.success();
        } catch (Exception e) {
            log.error("撤销权限申请失败", e);
            return Result.error("撤销权限申请失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户ID（简化实现，实际应该从认证信息中获取）
     */
    private String getCurrentUserId(HttpServletRequest request) {
        // 这里简化处理，实际应该从JWT token或session中获取
        String userId = request.getHeader("X-User-Id");
        return userId != null ? userId : "test-user";
    }

    /**
     * 获取当前用户名（简化实现，实际应该从认证信息中获取）
     */
    private String getCurrentUserName(HttpServletRequest request) {
        // 这里简化处理，实际应该从JWT token或session中获取
        String userName = request.getHeader("X-User-Name");
        return userName != null ? userName : "测试用户";
    }
}
