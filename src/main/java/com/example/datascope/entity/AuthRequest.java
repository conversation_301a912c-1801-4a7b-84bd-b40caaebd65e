package com.example.datascope.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 权限申请实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("auth_request")
public class AuthRequest {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源类型：DATASOURCE, SCHEMA, TABLE, COLUMN
     */
    private String resourceType;

    /**
     * 资源ID
     */
    private String resourceId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 申请理由
     */
    private String reason;

    /**
     * 申请用户ID
     */
    private String userId;

    /**
     * 申请用户名
     */
    private String userName;

    /**
     * 申请状态：PENDING, APPROVED, REJECTED, EXPIRED
     */
    private String status;

    /**
     * 申请时间
     */
    private LocalDateTime requestTime;

    /**
     * 审批时间
     */
    private LocalDateTime approvalTime;

    /**
     * 审批人
     */
    private String approvedBy;

    /**
     * 拒绝理由
     */
    private String rejectionReason;

    /**
     * 权限过期时间
     */
    private LocalDateTime expiryTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
