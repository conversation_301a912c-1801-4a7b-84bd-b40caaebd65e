package com.example.datascope.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.datascope.dto.AuthRequestDTO;
import com.example.datascope.dto.UserPermissionStatusDTO;
import com.example.datascope.entity.AuthRequest;

/**
 * 权限申请服务接口
 */
public interface AuthRequestService {

    /**
     * 提交权限申请
     */
    AuthRequest submitRequest(AuthRequestDTO requestDTO, String userId, String userName);

    /**
     * 查询用户权限状态
     */
    UserPermissionStatusDTO getUserPermissionStatus(String userId, String resourceType, String resourceId);

    /**
     * 分页查询用户的权限申请
     */
    IPage<AuthRequest> getUserAuthRequests(String userId, Integer page, Integer size, String status, String resourceType);

    /**
     * 分页查询所有权限申请（管理员用）
     */
    IPage<AuthRequest> getAllAuthRequests(Integer page, Integer size, String status, String resourceType, String userId);

    /**
     * 审批权限申请
     */
    AuthRequest approveRequest(Long requestId, String approvedBy, String action, String rejectionReason);

    /**
     * 检查用户是否有权限
     */
    boolean hasPermission(String userId, String resourceType, String resourceId);

    /**
     * 获取权限申请详情
     */
    AuthRequest getRequestById(Long requestId);

    /**
     * 撤销权限申请
     */
    void revokeRequest(Long requestId, String userId);
}
