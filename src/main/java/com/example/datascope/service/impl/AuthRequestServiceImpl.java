package com.example.datascope.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.datascope.dto.AuthRequestDTO;
import com.example.datascope.dto.UserPermissionStatusDTO;
import com.example.datascope.entity.AuthRequest;
import com.example.datascope.mapper.AuthRequestMapper;
import com.example.datascope.service.AuthRequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 权限申请服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthRequestServiceImpl implements AuthRequestService {

    private final AuthRequestMapper authRequestMapper;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    @Transactional
    public AuthRequest submitRequest(AuthRequestDTO requestDTO, String userId, String userName) {
        // 检查是否已有待审核的申请
        AuthRequest existingRequest = authRequestMapper.selectUserResourceRequest(
                userId, requestDTO.getResourceType(), requestDTO.getResourceId()
        );

        if (existingRequest != null && "PENDING".equals(existingRequest.getStatus())) {
            throw new RuntimeException("您已有待审核的权限申请，请勿重复提交");
        }

        // 创建新的权限申请
        AuthRequest authRequest = new AuthRequest();
        BeanUtils.copyProperties(requestDTO, authRequest);
        authRequest.setUserId(userId);
        authRequest.setUserName(userName);
        authRequest.setStatus("PENDING");
        authRequest.setRequestTime(LocalDateTime.now());
        authRequest.setCreateTime(LocalDateTime.now());
        authRequest.setUpdateTime(LocalDateTime.now());

        authRequestMapper.insert(authRequest);
        log.info("用户 {} 提交权限申请: {}", userName, authRequest);

        return authRequest;
    }

    @Override
    public UserPermissionStatusDTO getUserPermissionStatus(String userId, String resourceType, String resourceId) {
        UserPermissionStatusDTO status = new UserPermissionStatusDTO();

        // 检查是否有有效权限
        List<AuthRequest> validPermissions = authRequestMapper.selectUserValidPermissions(
                userId, resourceType, resourceId
        );

        if (!validPermissions.isEmpty()) {
            AuthRequest permission = validPermissions.get(0);
            status.setHasPermission(true);
            status.setRequestStatus("APPROVED");
            status.setRequestId(permission.getId().toString());
            if (permission.getRequestTime() != null) {
                status.setRequestTime(permission.getRequestTime().format(FORMATTER));
            }
            if (permission.getApprovalTime() != null) {
                status.setApprovalTime(permission.getApprovalTime().format(FORMATTER));
            }
            if (permission.getExpiryTime() != null) {
                status.setExpiryTime(permission.getExpiryTime().format(FORMATTER));
            }
            status.setReason(permission.getReason());
            return status;
        }

        // 检查是否有待审核的申请
        AuthRequest pendingRequest = authRequestMapper.selectUserResourceRequest(
                userId, resourceType, resourceId
        );

        if (pendingRequest != null) {
            status.setHasPermission(false);
            status.setRequestStatus(pendingRequest.getStatus());
            status.setRequestId(pendingRequest.getId().toString());
            if (pendingRequest.getRequestTime() != null) {
                status.setRequestTime(pendingRequest.getRequestTime().format(FORMATTER));
            }
            if (pendingRequest.getApprovalTime() != null) {
                status.setApprovalTime(pendingRequest.getApprovalTime().format(FORMATTER));
            }
            status.setReason(pendingRequest.getReason());
        } else {
            status.setHasPermission(false);
        }

        return status;
    }

    @Override
    public IPage<AuthRequest> getUserAuthRequests(String userId, Integer page, Integer size, String status, String resourceType) {
        Page<AuthRequest> pageParam = new Page<>(page != null ? page : 1, size != null ? size : 20);
        return authRequestMapper.selectUserAuthRequests(pageParam, userId, status, resourceType);
    }

    @Override
    public IPage<AuthRequest> getAllAuthRequests(Integer page, Integer size, String status, String resourceType, String userId) {
        Page<AuthRequest> pageParam = new Page<>(page != null ? page : 1, size != null ? size : 20);
        return authRequestMapper.selectAllAuthRequests(pageParam, status, resourceType, userId);
    }

    @Override
    @Transactional
    public AuthRequest approveRequest(Long requestId, String approvedBy, String action, String rejectionReason) {
        AuthRequest authRequest = authRequestMapper.selectById(requestId);
        if (authRequest == null) {
            throw new RuntimeException("权限申请不存在");
        }

        if (!"PENDING".equals(authRequest.getStatus())) {
            throw new RuntimeException("该申请已被处理");
        }

        authRequest.setStatus(action);
        authRequest.setApprovalTime(LocalDateTime.now());
        authRequest.setApprovedBy(approvedBy);
        authRequest.setUpdateTime(LocalDateTime.now());

        if ("APPROVED".equals(action)) {
            // 设置权限过期时间（默认1年）
            authRequest.setExpiryTime(LocalDateTime.now().plusYears(1));
        } else if ("REJECTED".equals(action)) {
            authRequest.setRejectionReason(rejectionReason);
        }

        authRequestMapper.updateById(authRequest);
        log.info("权限申请 {} 被 {} {}", requestId, approvedBy, "APPROVED".equals(action) ? "批准" : "拒绝");

        return authRequest;
    }

    @Override
    public boolean hasPermission(String userId, String resourceType, String resourceId) {
        List<AuthRequest> validPermissions = authRequestMapper.selectUserValidPermissions(
                userId, resourceType, resourceId
        );
        return !validPermissions.isEmpty();
    }

    @Override
    public AuthRequest getRequestById(Long requestId) {
        return authRequestMapper.selectById(requestId);
    }

    @Override
    @Transactional
    public void revokeRequest(Long requestId, String userId) {
        AuthRequest authRequest = authRequestMapper.selectById(requestId);
        if (authRequest == null) {
            throw new RuntimeException("权限申请不存在");
        }

        if (!userId.equals(authRequest.getUserId())) {
            throw new RuntimeException("无权撤销此申请");
        }

        if (!"PENDING".equals(authRequest.getStatus())) {
            throw new RuntimeException("只能撤销待审核的申请");
        }

        authRequestMapper.deleteById(requestId);
        log.info("用户 {} 撤销权限申请 {}", userId, requestId);
    }
}
