<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.datascope.mapper.AuthRequestMapper">

    <!-- 分页查询用户的权限申请 -->
    <select id="selectUserAuthRequests" resultType="com.example.datascope.entity.AuthRequest">
        SELECT * FROM auth_request
        WHERE user_id = #{userId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="resourceType != null and resourceType != ''">
            AND resource_type = #{resourceType}
        </if>
        ORDER BY request_time DESC
    </select>

    <!-- 查询用户对特定资源的权限申请 -->
    <select id="selectUserResourceRequest" resultType="com.example.datascope.entity.AuthRequest">
        SELECT * FROM auth_request
        WHERE user_id = #{userId}
        AND resource_type = #{resourceType}
        AND resource_id = #{resourceId}
        AND status IN ('PENDING', 'APPROVED', 'REJECTED')
        ORDER BY request_time DESC
        LIMIT 1
    </select>

    <!-- 查询用户对特定资源的有效权限 -->
    <select id="selectUserValidPermissions" resultType="com.example.datascope.entity.AuthRequest">
        SELECT * FROM auth_request
        WHERE user_id = #{userId}
        AND resource_type = #{resourceType}
        AND resource_id = #{resourceId}
        AND status = 'APPROVED'
        AND (expiry_time IS NULL OR expiry_time > NOW())
        ORDER BY approval_time DESC
    </select>

    <!-- 分页查询所有权限申请（管理员用） -->
    <select id="selectAllAuthRequests" resultType="com.example.datascope.entity.AuthRequest">
        SELECT * FROM auth_request
        WHERE 1=1
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="resourceType != null and resourceType != ''">
            AND resource_type = #{resourceType}
        </if>
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        ORDER BY request_time DESC
    </select>

</mapper>
