-- 创建权限申请表
CREATE TABLE IF NOT EXISTS auth_request (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型：DATASOURCE, SCHEMA, TABLE, COLUMN',
    resource_id VARCHAR(255) NOT NULL COMMENT '资源ID',
    resource_name VARCHAR(500) NOT NULL COMMENT '资源名称',
    reason TEXT NOT NULL COMMENT '申请理由',
    user_id VARCHAR(100) NOT NULL COMMENT '申请用户ID',
    user_name VARCHAR(200) NOT NULL COMMENT '申请用户名',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '申请状态：PENDING, APPROVED, REJECTED, EXPIRED',
    request_time DATETIME NOT NULL COMMENT '申请时间',
    approval_time DATETIME NULL COMMENT '审批时间',
    approved_by VARCHAR(200) NULL COMMENT '审批人',
    rejection_reason TEXT NULL COMMENT '拒绝理由',
    expiry_time DATETIME NULL COMMENT '权限过期时间',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_user_resource (user_id, resource_type, resource_id),
    INDEX idx_status (status),
    INDEX idx_request_time (request_time),
    INDEX idx_user_id (user_id),
    INDEX idx_resource_type (resource_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限申请表';
